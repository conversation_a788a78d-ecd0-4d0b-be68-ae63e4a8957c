const Doctors = () => {
  const doctors = [
    {
      name: 'Dr. <PERSON>',
      specialty: 'Pediatrician',
      image: 'https://plus.unsplash.com/premium_photo-1661627338048-d5650cef65dd?q=80&w=870&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      bio: 'Dr<PERSON> has over 15 years of experience in pediatric care. She specializes in child development, immunizations, and treating childhood illnesses with a gentle, caring approach.',
      qualifications: ['MD - University of Dar es Salaam', 'Pediatric Residency - Muhimbili Hospital', 'Board Certified Pediatrician'],
      languages: ['English', 'Swahili', 'Sukuma']
    },
    {
      name: 'Dr. <PERSON>',
      specialty: 'General Surgeon',
      image: 'https://images.pexels.com/photos/5452291/pexels-photo-5452291.jpeg',
      bio: 'Dr<PERSON> is a skilled general surgeon with expertise in both emergency and elective surgical procedures. He is known for his precision and compassionate patient care.',
      qualifications: ['MD - Makerere University', 'Surgery Residency - Kenyatta Hospital', 'Fellowship in Trauma Surgery'],
      languages: ['English', 'Swahili', 'Kikuyu']
    },
    {
      name: 'Dr. Grace Nyong\'o',
      specialty: 'Obstetrician & Gynecologist',
      image: 'https://images.pexels.com/photos/19963130/pexels-photo-19963130.jpeg',
      bio: 'Dr. Nyong\'o specializes in women\'s health, pregnancy care, and gynecological procedures. She has delivered over 2,000 babies and is passionate about maternal health.',
      qualifications: ['MD - University of Nairobi', 'OB/GYN Residency - Aga Khan Hospital', 'Maternal-Fetal Medicine Fellowship'],
      languages: ['English', 'Swahili', 'Luo']
    },
    {
      name: 'Dr. David Mushi',
      specialty: 'Internal Medicine',
      image: 'https://media.istockphoto.com/id/**********/photo/shot-of-a-young-male-doctor-standing-with-his-arms-crossed-in-an-office-at-a-hospital.webp?a=1&b=1&s=612x612&w=0&k=20&c=el0EsECW7YwH-YMFqt5BEk792-yFMHuyO6rDEu2itvw=',
      bio: 'Dr. Mushi is an internist with extensive experience in managing chronic diseases, diabetes, hypertension, and preventive care for adult patients.',
      qualifications: ['MD - Muhimbili University', 'Internal Medicine Residency', 'Endocrinology Fellowship'],
      languages: ['English', 'Swahili', 'Chagga']
    },
    {
      name: 'Dr. Sarah Kilonzo',
      specialty: 'Dentist',
      image: 'https://media.istockphoto.com/id/**********/photo/happy-successful-doctor-woman-medical-worker-in-white-lab-coat-standing-with-crossed-arms-on.webp?a=1&b=1&s=612x612&w=0&k=20&c=TwI1NORxnsq5M2OzsvHoDA3YC-DTQl7z3kjgSvT9RJk=',
      bio: 'Dr. Kilonzo provides comprehensive dental care for patients of all ages. She specializes in preventive dentistry, restorative procedures, and cosmetic dentistry.',
      qualifications: ['DDS - University of Nairobi', 'Advanced Restorative Dentistry Certificate', 'Cosmetic Dentistry Training'],
      languages: ['English', 'Swahili', 'Kamba']
    },
    {
      name: 'Dr. James Mbwana',
      specialty: 'Emergency Medicine',
      image: 'https://media.istockphoto.com/id/**********/photo/portrait-of-mature-male-doctor-wearing-white-coat-standing-in-hospital-corridor.jpg?s=612x612&w=0&k=20&c=Hk-dqLqHXyYa4aTqjieXNk9-HQSE8WEYUAjA1sXsy_s=',
      bio: 'Dr. Mbwana leads our emergency department with over 12 years of experience in emergency medicine. He is trained in advanced life support and trauma care.',
      qualifications: ['MD - Muhimbili University', 'Emergency Medicine Residency', 'Advanced Trauma Life Support Certified'],
      languages: ['English', 'Swahili', 'Makonde']
    }
  ]

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#0077B6] to-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Meet Our Doctors</h1>
            <p className="text-xl max-w-3xl mx-auto">
              Our team of experienced and dedicated medical professionals is committed to providing you with the highest quality healthcare
            </p>
          </div>
        </div>
      </section>

      {/* Doctors Grid */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {doctors.map((doctor, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden">
                <div className="aspect-w-1 aspect-h-1">
                  <img
                    src={doctor.image}
                    alt={doctor.name}
                    className="w-full h-64 object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-1">
                    {doctor.name}
                  </h3>
                  <p className="text-[#0077B6] font-medium mb-3">
                    {doctor.specialty}
                  </p>
                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    {doctor.bio}
                  </p>
                  
                  {/* Qualifications */}
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-900 mb-2 text-sm">Qualifications:</h4>
                    <ul className="space-y-1">
                      {doctor.qualifications.map((qualification, qualIndex) => (
                        <li key={qualIndex} className="text-xs text-gray-600 flex items-start">
                          <svg className="h-3 w-3 text-[#0077B6] mr-1 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                          </svg>
                          {qualification}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Languages */}
                  <div className="border-t border-gray-200 pt-4">
                    <h4 className="font-semibold text-gray-900 mb-2 text-sm">Languages:</h4>
                    <div className="flex flex-wrap gap-1">
                      {doctor.languages.map((language, langIndex) => (
                        <span
                          key={langIndex}
                          className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs"
                        >
                          {language}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Our Doctors */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose Our Medical Team?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our doctors are not just highly qualified professionals, but also compassionate caregivers committed to your wellbeing
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-[#0077B6] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Highly Qualified</h3>
              <p className="text-gray-600">
                All our doctors are board-certified with extensive training from reputable medical institutions
              </p>
            </div>
            <div className="text-center">
              <div className="bg-[#0077B6] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Compassionate Care</h3>
              <p className="text-gray-600">
                We believe in treating patients with empathy, respect, and understanding
              </p>
            </div>
            <div className="text-center">
              <div className="bg-[#0077B6] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Continuous Learning</h3>
              <p className="text-gray-600">
                Our medical team stays updated with the latest medical advances and best practices
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-[#0077B6] text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Schedule an Appointment</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Ready to meet with one of our experienced doctors? Contact us to schedule your appointment today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="bg-white text-[#0077B6] px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Book Appointment
            </a>
            <a
              href="tel:+255123456789"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-[#0077B6] transition-colors"
            >
              Call Now
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Doctors
