const Services = () => {
  const services = [
    {
      title: 'General Consultation',
      description: 'Comprehensive medical consultations with our experienced general practitioners. We provide thorough examinations, diagnosis, and treatment plans for a wide range of health conditions.',
      icon: (
        <svg className="h-12 w-12 text-[#0077B6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
        </svg>
      ),
      features: ['Health screenings', 'Preventive care', 'Chronic disease management', 'Health counseling']
    },
    {
      title: 'Maternity Services',
      description: 'Complete maternity care from pregnancy through delivery and postpartum care. Our experienced team ensures the safety and comfort of both mother and baby.',
      icon: (
        <svg className="h-12 w-12 text-[#0077B6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
        </svg>
      ),
      features: ['Prenatal care', 'Delivery services', 'Postnatal care', 'Family planning']
    },
    {
      title: 'Laboratory Services',
      description: 'State-of-the-art laboratory facilities providing accurate and timely diagnostic testing to support effective treatment decisions.',
      icon: (
        <svg className="h-12 w-12 text-[#0077B6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
        </svg>
      ),
      features: ['Blood tests', 'Urine analysis', 'Microbiology', 'Pathology']
    },
    {
      title: 'Dental Care',
      description: 'Comprehensive dental services for patients of all ages, from routine cleanings to advanced dental procedures.',
      icon: (
        <svg className="h-12 w-12 text-[#0077B6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
      ),
      features: ['Dental cleanings', 'Fillings', 'Extractions', 'Orthodontics']
    },
    {
      title: 'Surgery',
      description: 'Modern surgical facilities with experienced surgeons performing both minor and major surgical procedures with the highest safety standards.',
      icon: (
        <svg className="h-12 w-12 text-[#0077B6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
        </svg>
      ),
      features: ['General surgery', 'Emergency surgery', 'Outpatient procedures', 'Post-operative care']
    },
    {
      title: 'Pharmacy',
      description: 'Full-service pharmacy providing prescription medications, over-the-counter drugs, and pharmaceutical counseling services.',
      icon: (
        <svg className="h-12 w-12 text-[#0077B6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
        </svg>
      ),
      features: ['Prescription filling', 'Medication counseling', 'Health products', 'Insurance billing']
    }
  ]

  return (
    <div>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#0077B6] to-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Medical Services</h1>
            <p className="text-xl max-w-3xl mx-auto">
              Comprehensive healthcare services designed to meet all your medical needs with excellence and compassion
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
                <div className="flex justify-center mb-4">
                  {service.icon}
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3 text-center">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-4 text-center">
                  {service.description}
                </p>
                <div className="border-t border-gray-200 pt-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Services Include:</h4>
                  <ul className="space-y-1">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <svg className="h-4 w-4 text-[#0077B6] mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Services */}
      <section className="py-16 bg-red-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-red-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              </svg>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">24/7 Emergency Services</h2>
            <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
              Our emergency department is staffed around the clock with experienced medical professionals 
              ready to handle any medical emergency.
            </p>
            <div className="bg-white p-6 rounded-lg shadow-md inline-block">
              <p className="text-2xl font-bold text-red-600 mb-2">Emergency Hotline</p>
              <a href="tel:+255123456789" className="text-3xl font-bold text-gray-900 hover:text-[#0077B6]">
                +255 123 456 789
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-[#0077B6] text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Schedule an Appointment?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Contact us today to book your appointment or learn more about our services
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="bg-white text-[#0077B6] px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Book Appointment
            </a>
            <a
              href="tel:+255123456789"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-[#0077B6] transition-colors"
            >
              Call Now
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Services
